# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings

# Set display options
pd.set_option('display.max_columns', None)
warnings.filterwarnings('ignore')

# Set plotting styles
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 6)

# Load the cleaned dataset with error handling
import os

data_path = '../data/cleaned_transactions.csv'
if os.path.exists(data_path):
    df = pd.read_csv(data_path)
    print(f"✓ Dataset loaded successfully. Shape: {df.shape}")
    print(f"✓ Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"✓ Date range: Step {df['step'].min()} to {df['step'].max()}")
else:
    print("❌ Cleaned dataset not found!")
    print("Please run Week 1 notebook first to generate the cleaned dataset.")
    df = None

# Only proceed if dataset was loaded successfully
if df is not None:
    # 1. Transaction Amount Analysis
    print("=== TRANSACTION AMOUNT ANALYSIS ===")
    print(f"Amount statistics:")
    print(f"- Mean: ${df['amount'].mean():,.2f}")
    print(f"- Median: ${df['amount'].median():,.2f}")
    print(f"- Max: ${df['amount'].max():,.2f}")
    print(f"- Min: ${df['amount'].min():,.2f}")
    
    # Create subplots for amount analysis
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Histogram of amounts (log scale for better visualization)
    axes[0,0].hist(df['amount'], bins=50, alpha=0.7, color='skyblue')
    axes[0,0].set_xlabel('Amount')
    axes[0,0].set_ylabel('Frequency')
    axes[0,0].set_title('Distribution of Transaction Amounts')
    axes[0,0].set_yscale('log')
    
    # Box plot by transaction type
    df.boxplot(column='amount', by='type', ax=axes[0,1])
    axes[0,1].set_title('Transaction Amounts by Type')
    axes[0,1].set_xlabel('Transaction Type')
    axes[0,1].set_ylabel('Amount')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # Transaction volume over time
    transactions_over_time = df.groupby('step').size()
    axes[1,0].plot(transactions_over_time.index, transactions_over_time.values, color='green', alpha=0.7)
    axes[1,0].set_xlabel('Time Step (Hours)')
    axes[1,0].set_ylabel('Number of Transactions')
    axes[1,0].set_title('Transaction Volume Over Time')
    axes[1,0].grid(True, alpha=0.3)
    
    # Transaction type distribution
    type_counts = df['type'].value_counts()
    axes[1,1].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%', startangle=90)
    axes[1,1].set_title('Transaction Type Distribution')
    
    plt.tight_layout()
    plt.show()
    
    # Summary statistics by transaction type
    print("\n=== TRANSACTION STATISTICS BY TYPE ===")
    type_stats = df.groupby('type')['amount'].agg(['count', 'mean', 'median', 'std', 'min', 'max']).round(2)
    display(type_stats)
    
else:
    print("Cannot proceed with analysis - dataset not loaded.")

# Only proceed if dataset was loaded successfully
if df is not None:
    print("=== FRAUD ANALYSIS ===")
    
    # Overall fraud statistics
    total_fraud = df['isFraud'].sum()
    fraud_rate = df['isFraud'].mean() * 100
    print(f"Total fraudulent transactions: {total_fraud:,}")
    print(f"Overall fraud rate: {fraud_rate:.3f}%")
    
    # Fraud analysis by transaction type
    fraud_analysis = df.groupby('type').agg({
        'isFraud': ['sum', 'count', 'mean']
    }).round(4)
    fraud_analysis.columns = ['Fraud_Count', 'Total_Count', 'Fraud_Rate']
    fraud_analysis['Fraud_Rate_Percent'] = fraud_analysis['Fraud_Rate'] * 100
    
    print("\nFraud statistics by transaction type:")
    display(fraud_analysis)
    
    # Create comprehensive fraud visualizations
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Fraud rate by transaction type
    fraud_analysis['Fraud_Rate_Percent'].plot(kind='bar', ax=axes[0,0], color='red', alpha=0.7)
    axes[0,0].set_title('Fraud Rate by Transaction Type')
    axes[0,0].set_xlabel('Transaction Type')
    axes[0,0].set_ylabel('Fraud Rate (%)')
    axes[0,0].tick_params(axis='x', rotation=45)
    axes[0,0].grid(axis='y', alpha=0.3)
    
    # 2. Amount comparison: Fraud vs Non-fraud
    fraud_amounts = df[df['isFraud'] == 1]['amount']
    normal_amounts = df[df['isFraud'] == 0]['amount']
    
    axes[0,1].hist([normal_amounts, fraud_amounts], bins=50, alpha=0.7, 
                   label=['Normal', 'Fraud'], color=['blue', 'red'])
    axes[0,1].set_xlabel('Transaction Amount')
    axes[0,1].set_ylabel('Frequency')
    axes[0,1].set_title('Amount Distribution: Normal vs Fraud')
    axes[0,1].legend()
    axes[0,1].set_yscale('log')
    
    # 3. Fraud over time
    fraud_over_time = df.groupby('step')['isFraud'].mean() * 100
    axes[1,0].plot(fraud_over_time.index, fraud_over_time.values, color='red', alpha=0.7)
    axes[1,0].set_xlabel('Time Step (Hours)')
    axes[1,0].set_ylabel('Fraud Rate (%)')
    axes[1,0].set_title('Fraud Rate Over Time')
    axes[1,0].grid(True, alpha=0.3)
    
    # 4. Transaction volume: Normal vs Fraud
    volume_comparison = df.groupby(['step', 'isFraud']).size().unstack(fill_value=0)
    volume_comparison.plot(ax=axes[1,1], alpha=0.7)
    axes[1,1].set_xlabel('Time Step (Hours)')
    axes[1,1].set_ylabel('Number of Transactions')
    axes[1,1].set_title('Transaction Volume: Normal vs Fraud')
    axes[1,1].legend(['Normal', 'Fraud'])
    
    plt.tight_layout()
    plt.show()
    
    # Statistical comparison of fraud vs normal transactions
    print("\n=== FRAUD vs NORMAL TRANSACTION COMPARISON ===")
    comparison_stats = df.groupby('isFraud')['amount'].agg(['count', 'mean', 'median', 'std']).round(2)
    comparison_stats.index = ['Normal', 'Fraud']
    display(comparison_stats)
    
else:
    print("Cannot proceed with fraud analysis - dataset not loaded.")

# Only proceed if dataset was loaded successfully
if df is not None:
    print("=== BALANCE ANALYSIS ===")
    
    # Calculate balance changes
    df['balance_change_orig'] = df['newbalanceOrig'] - df['oldbalanceOrg']
    df['balance_change_dest'] = df['newbalanceDest'] - df['oldbalanceDest']
    
    # Check for balance inconsistencies (potential data quality issues)
    print("Balance consistency checks:")
    
    # For most transaction types, the amount should equal the balance change
    # (This might not hold for all transaction types in the simulation)
    inconsistent_orig = df[abs(df['balance_change_orig'] + df['amount']) > 0.01]
    inconsistent_dest = df[abs(df['balance_change_dest'] - df['amount']) > 0.01]
    
    print(f"- Transactions with origin balance inconsistencies: {len(inconsistent_orig):,}")
    print(f"- Transactions with destination balance inconsistencies: {len(inconsistent_dest):,}")
    
    # Create comprehensive balance analysis
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Balance changes by transaction type (Origin)
    df.boxplot(column='balance_change_orig', by='type', ax=axes[0,0])
    axes[0,0].set_title('Origin Balance Changes by Transaction Type')
    axes[0,0].set_xlabel('Transaction Type')
    axes[0,0].set_ylabel('Balance Change')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Balance changes by transaction type (Destination)
    df.boxplot(column='balance_change_dest', by='type', ax=axes[0,1])
    axes[0,1].set_title('Destination Balance Changes by Transaction Type')
    axes[0,1].set_xlabel('Transaction Type')
    axes[0,1].set_ylabel('Balance Change')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # 3. Zero-balance analysis
    zero_balance_data = {
        'Origin Old': (df['oldbalanceOrg'] == 0).mean() * 100,
        'Origin New': (df['newbalanceOrig'] == 0).mean() * 100,
        'Dest Old': (df['oldbalanceDest'] == 0).mean() * 100,
        'Dest New': (df['newbalanceDest'] == 0).mean() * 100
    }
    
    axes[1,0].bar(zero_balance_data.keys(), zero_balance_data.values(), color='orange', alpha=0.7)
    axes[1,0].set_title('Percentage of Zero-Balance Occurrences')
    axes[1,0].set_ylabel('Percentage (%)')
    axes[1,0].tick_params(axis='x', rotation=45)
    axes[1,0].grid(axis='y', alpha=0.3)
    
    # 4. Balance patterns in fraud vs normal transactions
    fraud_balance_orig = df[df['isFraud'] == 1]['oldbalanceOrg']
    normal_balance_orig = df[df['isFraud'] == 0]['oldbalanceOrg']
    
    axes[1,1].hist([normal_balance_orig, fraud_balance_orig], bins=50, alpha=0.7,
                   label=['Normal', 'Fraud'], color=['blue', 'red'])
    axes[1,1].set_xlabel('Origin Balance Before Transaction')
    axes[1,1].set_ylabel('Frequency')
    axes[1,1].set_title('Origin Balance Distribution: Normal vs Fraud')
    axes[1,1].legend()
    axes[1,1].set_yscale('log')
    
    plt.tight_layout()
    plt.show()
    
    # Balance statistics by fraud status
    print("\n=== BALANCE STATISTICS BY FRAUD STATUS ===")
    balance_fraud_stats = df.groupby('isFraud')[['oldbalanceOrg', 'newbalanceOrig', 'oldbalanceDest', 'newbalanceDest']].mean().round(2)
    balance_fraud_stats.index = ['Normal', 'Fraud']
    display(balance_fraud_stats)
    
else:
    print("Cannot proceed with balance analysis - dataset not loaded.")