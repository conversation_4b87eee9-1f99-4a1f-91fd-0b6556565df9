import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np

# Set page config
st.set_page_config(
    page_title="Mobile Money Transaction Dashboard",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Define consistent color scheme for fraud analysis
FRAUD_COLORS = {
    'normal': '#1f77b4',  # Blue for legitimate transactions
    'fraud': '#d62728',   # Red for fraudulent transactions
    'normal_name': 'Legitimate',
    'fraud_name': 'Fraudulent'
}

# Custom CSS for better styling
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}
.metric-container {
    background: linear-gradient(90deg, #f0f2f6, #ffffff);
    padding: 1rem;
    border-radius: 10px;
    border-left: 5px solid #1f77b4;
    margin: 0.5rem 0;
}
.sidebar .sidebar-content {
    background: linear-gradient(180deg, #f0f2f6, #ffffff);
}
.insight-box {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #28a745;
    margin: 1rem 0;
}
.warning-box {
    background: #fff3cd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

# Load data with enhanced error handling
@st.cache_data(ttl=3600, show_spinner="Loading transaction data...")
def load_data():
    """Load and cache transaction data with proper error handling"""
    try:
        df = pd.read_csv('data/cleaned_transactions.csv')

        # Ensure proper data types for performance
        if 'type' in df.columns:
            df['type'] = df['type'].astype('category')
        if 'isFraud' in df.columns:
            df['isFraud'] = df['isFraud'].astype('int8')

        return df
    except FileNotFoundError:
        st.error("📁 **Data file not found!**")
        st.info("Please run the Week 1 notebook first to generate the cleaned dataset.")
        st.markdown("**Steps to fix:**")
        st.markdown("1. Open `notebooks/Week1_Data_Loading_and_Cleaning.ipynb`")
        st.markdown("2. Run all cells to generate `data/cleaned_transactions.csv`")
        st.markdown("3. Refresh this dashboard")
        st.stop()
    except Exception as e:
        st.error(f"Error loading data: {str(e)}")
        st.stop()

# Load the data
with st.spinner("Initializing dashboard..."):
    df = load_data()

# Display data info in sidebar
st.sidebar.markdown("### 📊 Dataset Info")
st.sidebar.info(f"""
**Total Transactions:** {len(df):,}
**Date Range:** {df['step'].min()} - {df['step'].max()} hours
**Transaction Types:** {df['type'].nunique()}
**Fraud Rate:** {df['isFraud'].mean()*100:.3f}%
""")

# Enhanced sidebar filters
st.sidebar.markdown("---")
st.sidebar.markdown("### 🎛️ Dashboard Filters")

# Transaction type filter with better UX
st.sidebar.markdown("**Transaction Types**")
transaction_types = ['All'] + sorted(list(df['type'].unique()))
selected_type = st.sidebar.selectbox(
    'Select transaction type:',
    transaction_types,
    help="Filter transactions by type"
)

# Fraud filter with better labels
st.sidebar.markdown("**Fraud Analysis**")
fraud_filter = st.sidebar.radio(
    'Transaction status:',
    ['All Transactions', 'Fraudulent Only', 'Legitimate Only'],
    help="Filter by fraud status"
)

# Amount range filter with better formatting
st.sidebar.markdown("**Amount Range**")
min_amount = float(df['amount'].min())
max_amount = float(df['amount'].max())

# Use log scale for better UX with large ranges
amount_range = st.sidebar.slider(
    'Transaction amount ($):',
    min_value=min_amount,
    max_value=max_amount,
    value=(min_amount, max_amount),
    format="$%.2f",
    help="Filter transactions by amount range"
)

# Time range filter
st.sidebar.markdown("**Time Period**")
time_range = st.sidebar.slider(
    'Time range (hours):',
    min_value=int(df['step'].min()),
    max_value=int(df['step'].max()),
    value=(int(df['step'].min()), int(df['step'].max())),
    help="Filter by time period in simulation"
)

# Enhanced data filtering with progress indication
@st.cache_data(ttl=300)
def apply_filters(df, selected_type, fraud_filter, amount_range, time_range):
    """Apply filters to dataframe with caching for performance"""
    filtered_df = df.copy()

    # Transaction type filter
    if selected_type != 'All':
        filtered_df = filtered_df[filtered_df['type'] == selected_type]

    # Fraud filter
    if fraud_filter == 'Fraudulent Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 1]
    elif fraud_filter == 'Legitimate Only':
        filtered_df = filtered_df[filtered_df['isFraud'] == 0]

    # Amount range filter
    filtered_df = filtered_df[
        (filtered_df['amount'] >= amount_range[0]) &
        (filtered_df['amount'] <= amount_range[1])
    ]

    # Time range filter
    filtered_df = filtered_df[
        (filtered_df['step'] >= time_range[0]) &
        (filtered_df['step'] <= time_range[1])
    ]

    return filtered_df

# Apply filters
filtered_df = apply_filters(df, selected_type, fraud_filter, amount_range, time_range)

# Show filter results
if len(filtered_df) == 0:
    st.warning("⚠️ No transactions match the current filters. Please adjust your selection.")
    st.stop()

# Main dashboard with enhanced header
st.markdown('<h1 class="main-header">💰 Mobile Money Transaction Dashboard</h1>', unsafe_allow_html=True)
st.markdown("**Real-time analysis of mobile money transactions with fraud detection insights**")
st.markdown("---")

# Enhanced key metrics with delta calculations
st.markdown("## 📊 Key Performance Indicators")

# Calculate baseline metrics for comparison
baseline_fraud_rate = df['isFraud'].mean() * 100
baseline_avg_amount = df['amount'].mean()

col1, col2, col3, col4 = st.columns(4)

with col1:
    total_transactions = len(filtered_df)
    total_baseline = len(df)
    delta_transactions = total_transactions - total_baseline

    st.metric(
        "Total Transactions",
        f"{total_transactions:,}",
        delta=f"{delta_transactions:,}" if delta_transactions != 0 else None,
        help="Number of transactions matching current filters"
    )

with col2:
    total_volume = filtered_df['amount'].sum()
    st.metric(
        "Total Volume",
        f"${total_volume:,.0f}",
        help="Total transaction volume in dollars"
    )

with col3:
    fraud_rate = filtered_df['isFraud'].mean() * 100 if len(filtered_df) > 0 else 0
    delta_fraud = fraud_rate - baseline_fraud_rate

    st.metric(
        "Fraud Rate",
        f"{fraud_rate:.3f}%",
        delta=f"{delta_fraud:+.3f}%" if abs(delta_fraud) > 0.001 else None,
        delta_color="inverse",
        help="Percentage of fraudulent transactions"
    )

with col4:
    avg_amount = filtered_df['amount'].mean() if len(filtered_df) > 0 else 0
    delta_avg = avg_amount - baseline_avg_amount

    st.metric(
        "Average Amount",
        f"${avg_amount:,.2f}",
        delta=f"${delta_avg:+,.2f}" if abs(delta_avg) > 1 else None,
        help="Average transaction amount"
    )

# Enhanced transaction patterns section
st.markdown("---")
st.markdown("## 📈 Transaction Analysis")

col1, col2 = st.columns(2)

with col1:
    # Enhanced transaction type distribution with fraud analysis
    type_fraud_analysis = filtered_df.groupby('type').agg({
        'isFraud': ['count', 'sum', 'mean'],
        'amount': 'sum'
    }).round(4)
    type_fraud_analysis.columns = ['Total_Count', 'Fraud_Count', 'Fraud_Rate', 'Total_Volume']
    type_fraud_analysis['Fraud_Rate_Percent'] = type_fraud_analysis['Fraud_Rate'] * 100

    # Create pie chart with business insights
    fig = px.pie(
        values=type_fraud_analysis['Total_Count'].values,
        names=type_fraud_analysis.index,
        title='Transaction Volume by Type<br><sub>Hover for fraud insights</sub>',
        hole=0.4,
        color_discrete_sequence=['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']
    )

    # Add fraud rate information to hover
    fraud_rates = type_fraud_analysis['Fraud_Rate_Percent'].values
    volumes = type_fraud_analysis['Total_Volume'].values

    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate="<b>%{label}</b><br>" +
                     "Count: %{value:,}<br>" +
                     "Percentage: %{percent}<br>" +
                     "Fraud Rate: %{customdata[0]:.3f}%<br>" +
                     "Total Volume: $%{customdata[1]:,.0f}<br>" +
                     "<extra></extra>",
        customdata=list(zip(fraud_rates, volumes))
    )

    fig.update_layout(
        showlegend=True,
        height=400,
        title_x=0.5
    )

    st.plotly_chart(fig, use_container_width=True)

    # Add insights box
    with st.expander("💡 Transaction Type Insights"):
        highest_fraud_type = type_fraud_analysis['Fraud_Rate_Percent'].idxmax()
        highest_fraud_rate = type_fraud_analysis['Fraud_Rate_Percent'].max()
        highest_volume_type = type_fraud_analysis['Total_Volume'].idxmax()

        st.markdown(f"""
        **Key Findings:**
        - **Highest Risk Type**: {highest_fraud_type} ({highest_fraud_rate:.3f}% fraud rate)
        - **Highest Volume Type**: {highest_volume_type} (${type_fraud_analysis.loc[highest_volume_type, 'Total_Volume']:,.0f})
        - **Total Transaction Types**: {len(type_fraud_analysis)} different types analyzed

        **Business Impact**: Focus fraud prevention efforts on {highest_fraud_type} transactions while maintaining smooth operations for high-volume {highest_volume_type} transactions.
        """)

with col2:
    # Enhanced amount distribution with fraud overlay and insights
    fig = px.histogram(
        filtered_df,
        x='amount',
        color='isFraud',
        title='Amount Distribution: Fraud vs Legitimate<br><sub>Red = Fraud, Blue = Legitimate</sub>',
        nbins=50,
        opacity=0.7,
        color_discrete_map={0: FRAUD_COLORS['normal'], 1: FRAUD_COLORS['fraud']},
        labels={'isFraud': 'Transaction Status', 'amount': 'Transaction Amount ($)'}
    )

    fig.update_layout(
        height=400,
        title_x=0.5,
        xaxis_title="Transaction Amount ($)",
        yaxis_title="Number of Transactions",
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )

    # Update legend labels with consistent naming
    fig.for_each_trace(lambda t: t.update(name=FRAUD_COLORS['normal_name'] if t.name == "0" else FRAUD_COLORS['fraud_name']))

    st.plotly_chart(fig, use_container_width=True)

    # Add meaningful insights about amount patterns
    with st.expander("💡 Amount Pattern Insights"):
        fraud_amounts = filtered_df[filtered_df['isFraud'] == 1]['amount']
        normal_amounts = filtered_df[filtered_df['isFraud'] == 0]['amount']

        if len(fraud_amounts) > 0 and len(normal_amounts) > 0:
            st.markdown(f"""
            **Amount Analysis:**
            - **Fraudulent Transactions**: Average ${fraud_amounts.mean():,.2f} (Median: ${fraud_amounts.median():,.2f})
            - **Legitimate Transactions**: Average ${normal_amounts.mean():,.2f} (Median: ${normal_amounts.median():,.2f})
            - **Risk Pattern**: {'Fraudulent amounts are typically higher' if fraud_amounts.mean() > normal_amounts.mean() else 'Fraudulent amounts are typically lower'} than legitimate ones

            **Business Insight**: {'Monitor high-value transactions more closely' if fraud_amounts.mean() > normal_amounts.mean() else 'Small-amount fraud may indicate account testing or micro-fraud patterns'}
            """)
        else:
            st.info("Insufficient data for amount pattern analysis with current filters.")

# Enhanced time series analysis
st.markdown("---")
st.markdown("## ⏰ Temporal Analysis")

# Create hourly analysis for better insights
filtered_df['hour'] = filtered_df['step'] % 24
hourly_data = filtered_df.groupby('hour').agg({
    'amount': ['sum', 'count'],
    'isFraud': ['sum', 'count']
}).round(2)

hourly_data.columns = ['total_volume', 'transaction_count', 'fraud_count', 'total_count']
hourly_data['fraud_rate'] = (hourly_data['fraud_count'] / hourly_data['total_count'] * 100).fillna(0)
hourly_data = hourly_data.reset_index()

# Create enhanced transaction volume chart (showing fraud transactions more clearly)
fig = go.Figure()

# Add normal transaction volume as area (background)
fig.add_trace(
    go.Scatter(
        x=hourly_data['hour'],
        y=hourly_data['transaction_count'] - hourly_data['fraud_count'],
        fill='tozeroy',
        mode='none',
        name=FRAUD_COLORS['normal_name'],
        fillcolor=FRAUD_COLORS['normal'],
        opacity=0.6,
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Normal Transactions: %{y:,}<br>" +
                     "<extra></extra>"
    )
)

# Add fraud transactions as line with markers (more visible)
fig.add_trace(
    go.Scatter(
        x=hourly_data['hour'],
        y=hourly_data['fraud_count'],
        mode='lines+markers',
        name=FRAUD_COLORS['fraud_name'],
        line=dict(color=FRAUD_COLORS['fraud'], width=4),
        marker=dict(size=8, color=FRAUD_COLORS['fraud']),
        opacity=1.0,
        hovertemplate="<b>Hour %{x}</b><br>" +
                     "Fraud Transactions: %{y:,}<br>" +
                     "Fraud Rate: %{customdata:.2f}%<br>" +
                     "<extra></extra>",
        customdata=hourly_data['fraud_rate']
    )
)

# Update layout
fig.update_layout(
    title='Hourly Transaction Volume: Normal vs Fraud<br><sub>Blue Area = Normal, Red Line = Fraud (Enhanced Visibility)</sub>',
    title_x=0.5,
    xaxis_title="Hour of Day",
    yaxis_title="Number of Transactions",
    hovermode='x unified',
    height=500,
    showlegend=True,
    legend=dict(
        orientation="h",
        yanchor="bottom",
        y=1.02,
        xanchor="right",
        x=1
    )
)

fig.update_xaxes(
    tickmode='linear',
    tick0=0,
    dtick=2
)

st.plotly_chart(fig, use_container_width=True)

# Add comprehensive insights box
with st.expander("💡 Temporal Pattern Insights & Business Recommendations"):
    if len(hourly_data) > 0:
        peak_hour = hourly_data.loc[hourly_data['fraud_rate'].idxmax(), 'hour']
        peak_fraud_rate = hourly_data['fraud_rate'].max()
        peak_volume_hour = hourly_data.loc[hourly_data['total_volume'].idxmax(), 'hour']
        low_fraud_hour = hourly_data.loc[hourly_data['fraud_rate'].idxmin(), 'hour']
        low_fraud_rate = hourly_data['fraud_rate'].min()

        # Calculate risk periods
        high_risk_hours = hourly_data[hourly_data['fraud_rate'] > hourly_data['fraud_rate'].mean() + hourly_data['fraud_rate'].std()]

        st.markdown(f"""
        **🚨 Risk Analysis:**
        - **Highest Risk Period**: {peak_hour}:00 ({peak_fraud_rate:.2f}% fraud rate)
        - **Safest Period**: {low_fraud_hour}:00 ({low_fraud_rate:.2f}% fraud rate)
        - **High-Risk Hours**: {len(high_risk_hours)} hours above average risk

        **📊 Business Patterns:**
        - **Peak Activity**: {peak_volume_hour}:00 (highest transaction volume)
        - **Average Fraud Rate**: {hourly_data['fraud_rate'].mean():.3f}%
        - **Risk Variation**: {hourly_data['fraud_rate'].std():.3f}% standard deviation

        **💼 Business Recommendations:**
        - **Enhanced Monitoring**: Increase fraud detection sensitivity during hours {', '.join([f"{h}:00" for h in high_risk_hours['hour'].tolist()])}
        - **Resource Allocation**: Deploy more fraud analysts during peak risk period ({peak_hour}:00)
        - **Customer Communication**: Consider transaction limits or additional verification during high-risk hours
        - **Operational Efficiency**: Use low-risk periods ({low_fraud_hour}:00) for system maintenance
        """)
    else:
        st.info("No temporal data available for analysis with current filters.")

# Add meaningful balance analysis section
st.markdown("---")
st.markdown("## 💰 Balance Pattern Analysis")

# Calculate balance-related metrics
filtered_df['balance_change_orig'] = filtered_df['newbalanceOrig'] - filtered_df['oldbalanceOrg']
filtered_df['balance_change_dest'] = filtered_df['newbalanceDest'] - filtered_df['oldbalanceDest']

col1, col2 = st.columns(2)

with col1:
    # Balance behavior by fraud status
    balance_fraud_comparison = filtered_df.groupby('isFraud').agg({
        'oldbalanceOrg': 'mean',
        'newbalanceOrig': 'mean',
        'oldbalanceDest': 'mean',
        'newbalanceDest': 'mean'
    }).round(2)

    # Create balance comparison chart
    fig = go.Figure()

    categories = ['Origin Before', 'Origin After', 'Destination Before', 'Destination After']
    legitimate_values = [
        balance_fraud_comparison.loc[0, 'oldbalanceOrg'],
        balance_fraud_comparison.loc[0, 'newbalanceOrig'],
        balance_fraud_comparison.loc[0, 'oldbalanceDest'],
        balance_fraud_comparison.loc[0, 'newbalanceDest']
    ]

    if 1 in balance_fraud_comparison.index:
        fraudulent_values = [
            balance_fraud_comparison.loc[1, 'oldbalanceOrg'],
            balance_fraud_comparison.loc[1, 'newbalanceOrig'],
            balance_fraud_comparison.loc[1, 'oldbalanceDest'],
            balance_fraud_comparison.loc[1, 'newbalanceDest']
        ]
    else:
        fraudulent_values = [0, 0, 0, 0]

    fig.add_trace(go.Bar(
        name=FRAUD_COLORS['normal_name'],
        x=categories,
        y=legitimate_values,
        marker_color=FRAUD_COLORS['normal'],
        opacity=0.8
    ))

    fig.add_trace(go.Bar(
        name=FRAUD_COLORS['fraud_name'],
        x=categories,
        y=fraudulent_values,
        marker_color=FRAUD_COLORS['fraud'],
        opacity=0.8
    ))

    fig.update_layout(
        title='Average Account Balances: Fraud vs Legitimate<br><sub>Comparing balance patterns</sub>',
        xaxis_title='Account Balance Type',
        yaxis_title='Average Balance ($)',
        barmode='group',
        height=400,
        title_x=0.5
    )

    st.plotly_chart(fig, use_container_width=True)

with col2:
    # Zero balance analysis - this is crucial for fraud detection
    zero_balance_analysis = {
        'Origin Before': (filtered_df['oldbalanceOrg'] == 0).sum(),
        'Origin After': (filtered_df['newbalanceOrig'] == 0).sum(),
        'Destination Before': (filtered_df['oldbalanceDest'] == 0).sum(),
        'Destination After': (filtered_df['newbalanceDest'] == 0).sum()
    }

    # Calculate fraud rates for zero balance accounts
    zero_balance_fraud_rates = {}
    for balance_type, column in [('Origin Before', 'oldbalanceOrg'), ('Origin After', 'newbalanceOrig'),
                                ('Destination Before', 'oldbalanceDest'), ('Destination After', 'newbalanceDest')]:
        zero_balance_mask = filtered_df[column] == 0
        if zero_balance_mask.sum() > 0:
            fraud_rate = filtered_df[zero_balance_mask]['isFraud'].mean() * 100
            zero_balance_fraud_rates[balance_type] = fraud_rate
        else:
            zero_balance_fraud_rates[balance_type] = 0

    fig = go.Figure()

    fig.add_trace(go.Bar(
        name='Zero Balance Count',
        x=list(zero_balance_analysis.keys()),
        y=list(zero_balance_analysis.values()),
        marker_color='#FFA500',
        opacity=0.7,
        yaxis='y'
    ))

    fig.add_trace(go.Scatter(
        name='Fraud Rate (%)',
        x=list(zero_balance_fraud_rates.keys()),
        y=list(zero_balance_fraud_rates.values()),
        mode='lines+markers',
        line=dict(color=FRAUD_COLORS['fraud'], width=3),
        marker=dict(size=10),
        yaxis='y2'
    ))

    fig.update_layout(
        title='Zero Balance Accounts & Fraud Risk<br><sub>Orange bars = count, Red line = fraud rate</sub>',
        xaxis_title='Balance Type',
        yaxis=dict(title='Number of Zero Balance Accounts', side='left'),
        yaxis2=dict(title='Fraud Rate (%)', side='right', overlaying='y'),
        height=400,
        title_x=0.5
    )

    st.plotly_chart(fig, use_container_width=True)

# Add comprehensive balance insights
with st.expander("💡 Balance Pattern Insights & Fraud Indicators"):
    if len(filtered_df) > 0:
        # Calculate key balance metrics
        total_zero_orig_before = (filtered_df['oldbalanceOrg'] == 0).sum()
        total_zero_orig_after = (filtered_df['newbalanceOrig'] == 0).sum()
        total_zero_dest_before = (filtered_df['oldbalanceDest'] == 0).sum()
        total_zero_dest_after = (filtered_df['newbalanceDest'] == 0).sum()

        # Fraud rates for zero balances
        fraud_rate_zero_orig = filtered_df[filtered_df['oldbalanceOrg'] == 0]['isFraud'].mean() * 100 if total_zero_orig_before > 0 else 0
        fraud_rate_zero_dest = filtered_df[filtered_df['oldbalanceDest'] == 0]['isFraud'].mean() * 100 if total_zero_dest_before > 0 else 0

        st.markdown(f"""
        **🔍 Balance Pattern Analysis:**
        - **Zero Origin Balances**: {total_zero_orig_before:,} accounts ({fraud_rate_zero_orig:.2f}% fraud rate)
        - **Zero Destination Balances**: {total_zero_dest_before:,} accounts ({fraud_rate_zero_dest:.2f}% fraud rate)
        - **Balance Drain Pattern**: {total_zero_orig_after:,} accounts emptied after transaction
        - **New Account Pattern**: {total_zero_dest_after:,} destination accounts with zero final balance

        **🚨 Fraud Risk Indicators:**
        - **High Risk**: Zero balance accounts show {'higher' if max(fraud_rate_zero_orig, fraud_rate_zero_dest) > filtered_df['isFraud'].mean() * 100 else 'similar'} fraud rates
        - **Suspicious Pattern**: {'Account draining behavior detected' if total_zero_orig_after > total_zero_orig_before else 'Normal balance patterns observed'}
        - **Money Mule Indicator**: {'High destination zero balances suggest potential money laundering' if total_zero_dest_after > len(filtered_df) * 0.1 else 'Destination balance patterns appear normal'}

        **💼 Business Recommendations:**
        - **Enhanced KYC**: Implement stricter verification for zero-balance account transactions
        - **Transaction Limits**: Consider lower limits for accounts with zero balances
        - **Real-time Monitoring**: Flag transactions that result in account balance draining
        - **Risk Scoring**: Include balance history in fraud risk calculations
        """)
    else:
        st.info("No balance data available for analysis with current filters.")

# Enhanced transaction details section
st.markdown("---")
st.markdown("## 🔍 Transaction Explorer")

# Add search functionality
search_term = st.text_input("🔎 Search transactions:", placeholder="Enter transaction ID, type, or amount...")

# Apply search filter
display_df = filtered_df.copy()
if search_term:
    # Search across multiple columns
    search_mask = (
        display_df['nameOrig'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['nameDest'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['type'].astype(str).str.contains(search_term, case=False, na=False) |
        display_df['amount'].astype(str).str.contains(search_term, case=False, na=False)
    )
    display_df = display_df[search_mask]

# Enhanced pagination controls
col1, col2, col3 = st.columns([1, 1, 2])

with col1:
    rows_per_page = st.selectbox('Rows per page:', [10, 25, 50, 100], index=1)

with col2:
    total_pages = len(display_df) // rows_per_page + (1 if len(display_df) % rows_per_page > 0 else 0)
    if total_pages > 0:
        current_page = st.number_input('Page:', min_value=1, max_value=total_pages, value=1)
    else:
        current_page = 1
        st.write("No results found")

with col3:
    if len(display_df) > 0:
        start_idx = (current_page - 1) * rows_per_page
        end_idx = min(start_idx + rows_per_page, len(display_df))
        st.info(f'📊 Showing {start_idx + 1}-{end_idx} of {len(display_df):,} transactions')
    else:
        st.warning("No transactions match your search criteria")

# Display enhanced dataframe
if len(display_df) > 0:
    # Select and rename columns for better display
    display_columns = {
        'step': 'Time Step',
        'type': 'Type',
        'amount': 'Amount ($)',
        'isFraud': 'Fraud',
        'nameOrig': 'From Account',
        'nameDest': 'To Account',
        'oldbalanceOrg': 'From Balance Before',
        'newbalanceOrig': 'From Balance After',
        'oldbalanceDest': 'To Balance Before',
        'newbalanceDest': 'To Balance After'
    }

    table_df = display_df[list(display_columns.keys())].iloc[start_idx:end_idx].copy()
    table_df = table_df.rename(columns=display_columns)

    # Format the dataframe for better display
    table_df['Amount ($)'] = table_df['Amount ($)'].apply(lambda x: f"${x:,.2f}")
    table_df['Fraud'] = table_df['Fraud'].map({0: '✅ No', 1: '🚨 Yes'})

    # Format balance columns
    for col in ['From Balance Before', 'From Balance After', 'To Balance Before', 'To Balance After']:
        table_df[col] = table_df[col].apply(lambda x: f"${x:,.2f}")

    st.dataframe(
        table_df,
        use_container_width=True,
        height=400
    )

    # Add download button
    csv = display_df.to_csv(index=False)
    st.download_button(
        label="📥 Download filtered data as CSV",
        data=csv,
        file_name=f"mobile_money_transactions_{len(display_df)}_records.csv",
        mime="text/csv"
    )
else:
    st.info("Adjust your filters or search terms to see transaction data.")

# Footer with additional information
st.markdown("---")
st.markdown("### 📋 Dashboard Information")
st.info("""
**About this Dashboard:**
- Built with Streamlit and Plotly for interactive data exploration
- Data source: PaySim synthetic mobile money dataset
- Real-time filtering and analysis capabilities
- Fraud detection insights and pattern analysis

**How to use:**
1. Use the sidebar filters to narrow down transactions
2. Explore the visualizations to understand patterns
3. Use the transaction explorer to examine individual records
4. Download filtered data for further analysis
""")

st.markdown("---")
st.markdown("*Mobile Money Transaction Dashboard - Built with ❤️ using Streamlit*")
